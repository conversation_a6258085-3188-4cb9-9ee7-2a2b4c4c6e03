<template>
  <div class="min-h-screen bg-white dark:bg-zinc-900 py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header Section -->
      <div class="text-center mb-16">
        <div
          class="inline-block bg-zinc-100 dark:bg-zinc-800 text-zinc-600 dark:text-zinc-400 px-4 py-2 text-sm font-medium mb-4 tracking-wider"
        >
          CONTACT US
        </div>
        <h1
          class="text-4xl md:text-5xl font-bold text-zinc-900 dark:text-zinc-100 mb-4"
        >
          Get in touch with us
        </h1>
        <p class="text-lg text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
          Fill out the form below or schedule a meeting with us at your
          convenience.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
        <div class="space-y-6">
          <div
            v-if="showSuccess"
            class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 flex items-center space-x-3"
          >
            <Icon
              icon="heroicons:check-circle"
              class="w-5 h-5 text-green-600 dark:text-green-400"
            />
            <span>{{ successMessage }}</span>
          </div>

          <div
            v-if="errorMessage"
            class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 flex items-center space-x-3"
          >
            <Icon
              icon="heroicons:exclamation-triangle"
              class="w-5 h-5 text-red-600 dark:text-red-400"
            />
            <span>{{ errorMessage }}</span>
          </div>

          <FormKit
            type="form"
            @submit="handleSubmit"
            submit-label=""
            :actions="false"
            :disabled="loading"
          >
            <div class="space-y-3">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormKit
                  type="text"
                  name="first_name"
                  label="First Name"
                  placeholder="Enter your first name"
                  validation="required"
                  :disabled="loading"
                  input-class="w-full px-4 py-3 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-transparent"
                />
                <FormKit
                  type="text"
                  name="last_name"
                  label="Last Name"
                  placeholder="Enter your last name"
                  validation="required"
                  :disabled="loading"
                  input-class="w-full px-4 py-3 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-transparent"
                />
              </div>

              <FormKit
                type="email"
                name="email"
                label="Email"
                placeholder="Enter your email address"
                validation="required|email"
                :disabled="loading"
                input-class="w-full px-4 py-3 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />

              <FormKit
                type="text"
                name="phone"
                label="Phone Number"
                placeholder="Enter your phone number"
                validation="required|length:9,15"
                :disabled="loading"
                input-class="w-full px-4 py-3 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />

              <FormKit
                type="text"
                name="subject"
                label="Subject"
                placeholder="Subject of your message"
                validation="required"
                :disabled="loading"
                input-class="w-full px-4 py-3 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />

              <FormKit
                type="textarea"
                name="message"
                label="Message"
                placeholder="Enter your message"
                validation="required"
                :rows="4"
                :disabled="loading"
                input-class="w-full px-4 py-3 border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-500 dark:placeholder-zinc-400 focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
              />

              <label
                class="flex cursor-pointer space-x-3 py-2 text-sm text-zinc-600 dark:text-zinc-400"
              >
                <input
                  type="checkbox"
                  name="terms_agreement"
                  required
                  class="mr-2"
                />
                I agree with
                <a
                  href="/terms-usage-policy"
                  class="text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 underline"
                >
                  Terms and Conditions
                </a>
              </label>

              <CoreSubmitButton
                :loading="loading"
                :disabled="loading"
                text="Submit"
              />
            </div>
          </FormKit>
        </div>

        <div class="space-y-8">
          <div>
            <h3
              class="text-xl font-semibold text-zinc-900 dark:text-zinc-100 mb-6"
            >
              With our services you can
            </h3>
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Generate events revenue, reports, engagements with ease</span
                >
              </div>
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Engage users at a higher level and outperform your
                  event</span
                >
              </div>
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Find the perfect venue for your upcoming event</span
                >
              </div>
              <div class="flex items-center space-x-3">
                <div
                  class="flex-shrink-0 w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
                >
                  <Icon
                    icon="heroicons:check"
                    class="w-3 h-3 text-green-600 dark:text-green-400"
                  />
                </div>
                <span class="text-zinc-700 dark:text-zinc-300"
                  >Don't worry about event planning, vendors are right at the
                  corner</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useHttpClient } from "@/composables/useHttpClient";
import { ENDPOINTS } from "@/utils/api";

definePageMeta({
  layout: "default",
});

useHead({
  title: "Contact EventaHub Malawi - Get in Touch with Our Team",
  meta: [
    {
      name: "description",
      content:
        "Get in touch with EventaHub Malawi team. Contact us for support, partnerships, event hosting inquiries, or any questions about our event platform.",
    },
    {
      name: "keywords",
      content:
        "contact EventaHub, support Malawi, event platform support, customer service, get in touch",
    },
    {
      property: "og:title",
      content: "Contact EventaHub Malawi - Get in Touch with Our Team",
    },
    {
      property: "og:description",
      content:
        "Get in touch with EventaHub Malawi team. Contact us for support, partnerships, event hosting inquiries, or any questions.",
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      name: "twitter:card",
      content: "summary_large_image",
    },
    {
      name: "twitter:title",
      content: "Contact EventaHub Malawi - Get in Touch with Our Team",
    },
    {
      name: "twitter:description",
      content:
        "Get in touch with EventaHub Malawi team for support, partnerships, or event hosting inquiries.",
    },
    {
      name: "robots",
      content: "index, follow",
    },
  ],
});

const loading = ref<boolean>(false);
const showSuccess = ref<boolean>(false);
const successMessage = ref<string>("");
const errorMessage = ref<string>("");

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();

const handleSubmit = async (formData: any) => {
  if (loading.value) return;

  showSuccess.value = false;
  errorMessage.value = "";
  loading.value = true;

  try {
    const response = await httpClient.post<{
      success: boolean;
      message: string;
    }>(ENDPOINTS.CONTACT.SUBMIT, {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      message: formData.message,
      terms_agreement: formData.terms_agreement,
    });

    if (response.success) {
      successMessage.value =
        response.message ||
        "Thank you for your message! We will get back to you soon.";
      showSuccess.value = true;

      $toast.success(successMessage.value);

      setTimeout(() => {
        showSuccess.value = false;
      }, 5000);
    }
  } catch (error: any) {
    console.error("Contact form submission error:", error);
    handleErrorWithToast(error, $toast);
    setTimeout(() => {
      errorMessage.value = "";
    }, 8000);
  } finally {
    loading.value = false;
  }
};
</script>
